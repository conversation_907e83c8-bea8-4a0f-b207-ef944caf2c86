package main

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"strconv"
	"time"
)

var DB1 *gorm.DB

func main() {
	var err error
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second, // 慢 SQL 阈值
			LogLevel:                  logger.Info, // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略ErrRecordNotFound（记录未找到）错误
			Colorful:                  true,        // 禁用彩色打印
		},
	)
	dsn := "yxphp:cJvkmFOmWQ3y3Etk@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare_data"
	DB1, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}
	sqlDB1, _ := DB1.DB()
	sqlDB1.SetMaxIdleConns(1)
	sqlDB1.SetMaxOpenConns(1)

	//导出过滤后数据
	getActiveData()
}

func getActiveData() {
	//读取excel
	f, err := excelize.OpenFile("C28PRO.xlsx")
	if err != nil {
		fmt.Println(err)
		return
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	// 获取 Sheet1 上所有单元格
	sheetName := "Sheet1"
	rows, err := f.GetRows(sheetName)
	if err != nil {
		fmt.Println(err)
		return
	}

	for rk, row := range rows {
		if rk == 0 {
			f.SetColWidth(sheetName, "H", "H", 55)
			continue
		}
		barcode := row[0]
		//保卡数据
		var result struct {
			UpdateTime time.Time `gorm:"column:update_time"`
		}
		err = DB1.Table("mes_devices").Select("update_time").
			Where("barcode = ?", barcode).First(&result).Error
		if err != nil {
			fmt.Println(err)
			continue
		}
		/*if result.UpdateTime == "" {
			fmt.Println("update_time is empty for barcode:", barcode)
			continue
		}
		updateTime, err := time.Parse(time.DateTime, result.UpdateTime)
		if err != nil {
			fmt.Println(err)
			continue
		}*/
		err = f.SetCellValue(sheetName, "H"+strconv.Itoa(rk+1), result.UpdateTime)
		if err != nil {
			fmt.Println(err)
		}

		time.Sleep(20 * time.Millisecond)
	}
	err = f.Save()
	if err != nil {
		fmt.Println(err)
	}
}

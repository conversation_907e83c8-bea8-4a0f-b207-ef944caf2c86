package main

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"strconv"
	"time"
)

var DB1 *gorm.DB
var DB2 *gorm.DB

type Warranty struct {
	Id             int
	ActivatedAtOld string
	ActivatedId    int
	BuyDate        string
}

type AcDevicesUniq struct {
	ID         int    `gorm:"column:id;primary_key" json:"id"`
	Series     int8   `gorm:"column:series" json:"series"`
	ModelID    int    `gorm:"column:model_id" json:"model_id"`
	Model      string `gorm:"column:model" json:"model"`
	NavModel   string `gorm:"column:nav_model" json:"nav_model"`
	Uniqno     string `gorm:"column:uniqno" json:"uniqno"`
	Barcode    string `gorm:"column:barcode" json:"barcode"`
	Number     string `gorm:"column:number" json:"number"`
	IMEI       string `gorm:"column:imei" json:"imei"`
	Province   string `gorm:"column:province" json:"province"`
	City       string `gorm:"column:city" json:"city"`
	District   string `gorm:"column:district" json:"district"`
	Address    string `gorm:"column:address" json:"address"`
	Adcode     string `gorm:"column:adcode" json:"adcode"`
	Area       string `gorm:"column:area" json:"area"`
	Subarea    string `gorm:"column:subarea" json:"subarea"`
	AgencyID   uint   `gorm:"column:agency_id" json:"agency_id"`
	AgencyName string `gorm:"column:agency_name" json:"agency_name"`
	MesExists  int8   `gorm:"column:mes_exists" json:"mes_exists"`
	CustCode   string `gorm:"column:cust_code" json:"cust_code"`
	CustName   string `gorm:"column:cust_name" json:"cust_name"`
	Channel    string `gorm:"column:channel" json:"channel"`
	Exchanged  int8   `gorm:"column:exchanged" json:"exchanged"`
	Createat   string `gorm:"column:createat" json:"createat"`
	Status     int8   `gorm:"column:status" json:"status"`
	DeletedAt  string `gorm:"column:deleted_at" json:"deleted_at"`
	Bindat     string `gorm:"column:bindat" json:"bindat"`
	Location   string `gorm:"column:location" json:"location"`
	IP         string `gorm:"column:ip" json:"ip"`
	Origin     string `gorm:"column:origin" json:"origin"`
	UID        int    `gorm:"column:uid" json:"uid"`
	Did        int    `gorm:"column:did" json:"did"`
}

func main() {
	var err error
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second, // 慢 SQL 阈值
			LogLevel:                  logger.Info, // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略ErrRecordNotFound（记录未找到）错误
			Colorful:                  true,        // 禁用彩色打印
		},
	)
	dsn := "yxphp:cJvkmFOmWQ3y3Etk@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare"
	DB1, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}
	sqlDB1, _ := DB1.DB()
	sqlDB1.SetMaxIdleConns(1)
	sqlDB1.SetMaxOpenConns(1)

	dsn2 := "yxphp:cJvkmFOmWQ3y3Etk@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare_data"
	DB2, err = gorm.Open(mysql.Open(dsn2), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}
	sqlDB2, _ := DB2.DB()
	sqlDB2.SetMaxIdleConns(1)
	sqlDB2.SetMaxOpenConns(1)

	//导出过滤后数据
	getActiveData()
}

func getActiveData() {
	//读取excel
	f, err := excelize.OpenFile("20240725sale/2024年7月-12月寄修订单信息保内.xlsx")
	if err != nil {
		fmt.Println(err)
		return
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	// 获取 Sheet1 上所有单元格
	sheetName := "2024年07-12月保内"
	rows, err := f.GetRows(sheetName)
	if err != nil {
		fmt.Println(err)
		return
	}
	var i int
	for rk, row := range rows {
		i++
		if rk == 0 {
			f.SetColWidth(sheetName, "L", "O", 20)
			continue
		}
		barcode := row[3]
		//保卡数据
		var data Warranty
		err := DB1.Table("warranty").Select("id", "activated_at_old", "activated_id", "buy_date").
			Where("barcode = ?", barcode).Where("status = ?", 1).Order("id desc").
			Limit(1).Find(&data).Error
		if err != nil {
			fmt.Println(err)
		}
		fmt.Println(data)
		err = f.SetCellValue(sheetName, "L"+strconv.Itoa(rk+1), data.BuyDate)
		err = f.SetCellValue(sheetName, "M"+strconv.Itoa(rk+1), data.ActivatedAtOld)
		fmt.Println("卖出 ", data.BuyDate)
		fmt.Println("激活 ", data.ActivatedAtOld)

		//出货时间
		var billDate string
		err = DB2.Table("rbcare_data.v3_outstock").Select("bill_date").
			Where("barcode = ?", barcode).Scan(&billDate).Error
		err = f.SetCellValue(sheetName, "N"+strconv.Itoa(rk+1), billDate)
		fmt.Println("出货 ", billDate)

		//包装时间
		var packTime string
		err = DB1.Table("warranty_mes_back_up").Select("product_date").
			Where("barcode = ?", barcode).Scan(&packTime).Error
		err = f.SetCellValue(sheetName, "O"+strconv.Itoa(rk+1), packTime)
		fmt.Println("包装 ", packTime)

		if err != nil {
			fmt.Println(err)
		}
		time.Sleep(50 * time.Millisecond)

	}
	err = f.Save()
	if err != nil {
		fmt.Println(err)
	}
}

package main

import (
	"fmt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"time"
)

var DB1 *gorm.DB

func main() {
	var err error
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second, // 慢 SQL 阈值
			LogLevel:                  logger.Info, // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略ErrRecordNotFound（记录未找到）错误
			Colorful:                  true,        // 禁用彩色打印
		},
	)
	//这个是mes在阿里云上的库，内网的库实时同步到外网上，账号只有只读权限
	dsn := "rbproduction:BVBTdShnJ6@tcp(rm-wz9i89w1m7788168oyo.mysql.rds.aliyuncs.com)/mes"
	DB1, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}
	sqlDB1, _ := DB1.DB()
	sqlDB1.SetMaxIdleConns(1)
	sqlDB1.SetMaxOpenConns(1)
	dealData()
}

type V3SalOutstockSerial struct {
	Sid   uint
	RowId int
}

func (V3SalOutstockSerial) TableName() string {
	return "v3_sal_outstock_serial"
}

func dealData() {
	var rowIds []int64
	err := DB1.Table("v3_sal_outstock_serial as vs").
		Joins("LEFT JOIN v3_sal_outstock as vo on vo.id=vs.row_id").
		Select("vs.row_id").
		Where("vo.id is null").Group("row_id").Pluck("row_id", &rowIds).Error
	if err != nil {
		fmt.Println(err)
	}
	//fmt.Println(len(rowIds), rowIds)
	//删除无用的数据
	if len(rowIds) > 0 {
		var data1 []V3SalOutstockSerial
		err = DB1.Where("row_id IN (?)", rowIds).Find(&data1).Error
		if err != nil {
			fmt.Println(err)
		}
		fmt.Println(len(data1), data1)
		//该账号无权限删除，打印出sql 再自己执行一下吧
		// DELETE FROM `v3_sal_outstock_serial` WHERE row_id IN (1403525,1403743,1403759,1403818,1403822)
		RowsAffected := DB1.Where("row_id IN (?)", rowIds).Delete(&V3SalOutstockSerial{}).RowsAffected
		fmt.Println("删除行数：", RowsAffected)
	}

}

package main

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"strconv"
	"time"
)

var DB1 *gorm.DB

func main() {
	var err error
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second, // 慢 SQL 阈值
			LogLevel:                  logger.Info, // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略ErrRecordNotFound（记录未找到）错误
			Colorful:                  true,        // 禁用彩色打印
		},
	)
	dsn := "rbproduction:BVBTdShnJ6@tcp(rm-wz9i89w1m7788168oyo.mysql.rds.aliyuncs.com)/mes"
	DB1, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}
	sqlDB1, _ := DB1.DB()
	sqlDB1.SetMaxIdleConns(1)
	sqlDB1.SetMaxOpenConns(1)
	getNumber()
}

func getNumber() {
	//读取excel
	f, err := excelize.OpenFile("1.xlsx")
	if err != nil {
		fmt.Println("打开文件出错了：", err)
		return
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	// 获取 Sheet1 上所有单元格
	rows, err := f.GetRows("Sheet1")
	if err != nil {
		fmt.Println(err)
		return
	}
	//生成新的excel
	var (
		title = []string{
			"条码",
			"序列号",
		}
		titleStyle int
	)
	fn := excelize.NewFile()
	defer func() {
		if err := fn.Close(); err != nil {
			log.Printf("出错了：%v", err)
		}
	}()
	// 标题行样式
	if titleStyle, err = fn.NewStyle(&excelize.Style{
		Font:      &excelize.Font{Color: "1f7f3b", Bold: true, Family: "Microsoft YaHei"},
		Fill:      excelize.Fill{Type: "pattern", Color: []string{"E6F4EA"}, Pattern: 1},
		Alignment: &excelize.Alignment{Vertical: "center"},
		Border: []excelize.Border{
			{Type: "top", Style: 2, Color: "1f7f3b"},
			{Type: "bottom", Style: 1, Color: "1f7f3b"},
			{Type: "left", Style: 1, Color: "1f7f3b"}},
	}); err != nil {
		log.Printf("出错了：%v", err)
	}

	if err = fn.SetSheetRow("Sheet1", "A1", &title); err != nil {
		log.Printf("出错了：%v", err)
	}
	if err = fn.SetRowHeight("Sheet1", 1, 30); err != nil {
		log.Printf("出错了：%v", err)
	}
	// 设置标题行样式
	if err = fn.SetCellStyle("Sheet1", "A1", "B1", titleStyle); err != nil {
		log.Printf("出错了：%v", err)
	}
	// 设置列宽
	if err = fn.SetColWidth("Sheet1", "A", "B", 30); err != nil {
		log.Printf("出错了：%v", err)
	}

	i := 1
	for _, row := range rows {
		if len(row) == 0 {
			continue
		}
		barcode := row[0]
		if barcode == "" {
			continue
		}
		var number []string
		err = DB1.Table("v3_barcode").Where("barcode = ?", barcode).Pluck("number", &number).Error
		if err != nil {
			fmt.Println(barcode, ":", err)
			continue
		}
		i++
		var tempRow []interface{}
		number1 := ""
		if len(number) > 0 {
			number1 = number[0]
		}
		tempRow = append(tempRow, barcode)
		tempRow = append(tempRow, number1)
		if err = fn.SetSheetRow("Sheet1", "A"+strconv.Itoa(i), &tempRow); err != nil {
			log.Printf("出错了：%v", err)
		}

		//time.Sleep(1000 * time.Millisecond)
	}
	fn.SaveAs("sn.xlsx")
}

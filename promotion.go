package main

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"slices"
	"sort"
	"strconv"
	"time"
)

var start = "2024-09-15 00:00:00"
var end = "2024-09-18 00:00:00"
var name1 = "9月中秋大促符合条件数据.xlsx"
var name2 = "9月中秋大促原始数据.xlsx"
var model = []string{"C60", "C35", "C80Pro", "C80", "C70Pro", "C70", "C26Pro"}
var DB1 *gorm.DB
var DB2 *gorm.DB

type TerminalData struct {
	Zone                string
	Terminal            string
	Code                string
	Address             string
	Manager             string
	Phone               string
	Model               string
	Barcode             string
	Number              string
	CustomerName        string
	CustomerPhone       string
	StudentUID          string
	StudentName         string
	BuyDate             string
	ActivatedAtOld      string
	AgencyName          string
	BuyActivateHourDiff int
	ActivatedId         int
	ReturnAt            string
}

func main() {
	var err error
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second, // 慢 SQL 阈值
			LogLevel:                  logger.Info, // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略ErrRecordNotFound（记录未找到）错误
			Colorful:                  true,        // 禁用彩色打印
		},
	)
	dsn := "yxphp:cJvkmFOmWQ3y3Etk@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare"
	DB1, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}
	sqlDB1, _ := DB1.DB()
	sqlDB1.SetMaxIdleConns(1)
	sqlDB1.SetMaxOpenConns(1)

	dsn2 := "yxphp:cJvkmFOmWQ3y3Etk@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare_data"
	DB2, err = gorm.Open(mysql.Open(dsn2), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}
	sqlDB2, _ := DB2.DB()
	sqlDB2.SetMaxIdleConns(2)
	sqlDB2.SetMaxOpenConns(2)

	//导出过滤后数据
	importSuitData()
	importOriginData()
}

func importSuitData() {
	var (
		title = []string{
			"序号",
			"区域",
			"终端",
			"终端代码",
			"门店地址",
			"店长名称",
			"店长电话",
			"机型",
			"S/N码",
			"序列号",
			"客户姓名",
			"联系电话",
			"学生UID",
			"学生姓名",
			"购买时间",
			"激活时间",
			"激活手机号码",
			"购买激活小时差",
			"购买激活手机号是否一致",
		}
		titleStyle     int
		addr           string
		err            error
		ExistAgency    []string
		ExistAgencyMap = make(map[string]int)
	)
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			log.Printf("出错了：%v", err)
		}
	}()
	// 标题行样式
	if titleStyle, err = f.NewStyle(&excelize.Style{
		Font:      &excelize.Font{Color: "1f7f3b", Bold: true, Family: "Microsoft YaHei"},
		Fill:      excelize.Fill{Type: "pattern", Color: []string{"E6F4EA"}, Pattern: 1},
		Alignment: &excelize.Alignment{Vertical: "center"},
		Border: []excelize.Border{
			{Type: "top", Style: 2, Color: "1f7f3b"},
			{Type: "bottom", Style: 1, Color: "1f7f3b"},
			{Type: "left", Style: 1, Color: "1f7f3b"}},
	}); err != nil {
		log.Printf("出错了：%v", err)
	}
	data1, _ := getSuitData()
	var rk = 1
	if err = f.SetSheetRow("Sheet1", "A1", &title); err != nil {
		log.Printf("出错了：%v", err)
	}
	if err = f.SetRowHeight("Sheet1", 1, 30); err != nil {
		log.Printf("出错了：%v", err)
	}
	// 设置标题行样式
	if err = f.SetCellStyle("Sheet1", "A1", "T1", titleStyle); err != nil {
		log.Printf("出错了：%v", err)
	}
	// 设置列宽
	if err = f.SetColWidth("Sheet1", "A", "T", 20); err != nil {
		log.Printf("出错了：%v", err)
	}
	for _, row := range data1 {
		//查出手机号是否一致
		originPhone := ""

		err = DB2.Table("ac_devices_uniq").Where("id = ?", row.ActivatedId).Select("origin").Scan(&originPhone).Error
		if err != nil {
			fmt.Println(err)
			return
		}
		fmt.Println(originPhone)
		if originPhone != row.CustomerPhone {
			continue
		}
		rk++
		sort.Strings(ExistAgency)
		_, found := slices.BinarySearch(ExistAgency, row.AgencyName)
		if found == false {
			//新建工作表
			_, err = f.NewSheet(row.AgencyName)
			if err != nil {
				fmt.Println(err)
				return
			}
			//设置头部
			if err = f.SetSheetRow(row.AgencyName, "A1", &title); err != nil {
				log.Printf("出错了：%v", err)
			}
			if err = f.SetRowHeight(row.AgencyName, 1, 30); err != nil {
				log.Printf("出错了：%v", err)
			}
			// 设置标题行样式
			if err = f.SetCellStyle(row.AgencyName, "A1", "T1", titleStyle); err != nil {
				log.Printf("出错了：%v", err)
			}
			// 设置列宽
			if err = f.SetColWidth(row.AgencyName, "A", "T", 20); err != nil {
				log.Printf("出错了：%v", err)
			}

			//赋值
			if addr, err = excelize.JoinCellName("A", 2); err != nil {
				if err != nil {
					log.Printf("出错了：%v", err)
				}
			}
			var tempRow []interface{}
			tempRow = append(tempRow, 1)
			tempRow = append(tempRow, row.Zone)
			tempRow = append(tempRow, row.Terminal)
			tempRow = append(tempRow, row.Code)
			tempRow = append(tempRow, row.Address)
			tempRow = append(tempRow, row.Manager)
			tempRow = append(tempRow, row.Phone)
			tempRow = append(tempRow, row.Model)
			tempRow = append(tempRow, row.Barcode)
			tempRow = append(tempRow, row.Number)
			tempRow = append(tempRow, row.CustomerName)
			tempRow = append(tempRow, row.CustomerPhone)
			tempRow = append(tempRow, row.StudentUID)
			tempRow = append(tempRow, row.StudentName)
			tempRow = append(tempRow, row.BuyDate)
			tempRow = append(tempRow, row.ActivatedAtOld)
			tempRow = append(tempRow, originPhone)
			tempRow = append(tempRow, row.BuyActivateHourDiff)
			tempRow = append(tempRow, "是")
			if err = f.SetSheetRow(row.AgencyName, addr, &tempRow); err != nil {
				log.Printf("出错了：%v", err)
			}
			tempRow[0] = rk - 1
			if err = f.SetSheetRow("Sheet1", "A"+strconv.Itoa(rk), &tempRow); err != nil {
				log.Printf("出错了：%v", err)
			}
			ExistAgency = append(ExistAgency, row.AgencyName)
			ExistAgencyMap[row.AgencyName] = 2

		} else {
			if addr, err = excelize.JoinCellName("A", ExistAgencyMap[row.AgencyName]+1); err != nil {
				log.Printf("出错了：%v", err)
			}
			var tempRow []interface{}
			tempRow = append(tempRow, ExistAgencyMap[row.AgencyName])
			tempRow = append(tempRow, row.Zone)
			tempRow = append(tempRow, row.Terminal)
			tempRow = append(tempRow, row.Code)
			tempRow = append(tempRow, row.Address)
			tempRow = append(tempRow, row.Manager)
			tempRow = append(tempRow, row.Phone)
			tempRow = append(tempRow, row.Model)
			tempRow = append(tempRow, row.Barcode)
			tempRow = append(tempRow, row.Number)
			tempRow = append(tempRow, row.CustomerName)
			tempRow = append(tempRow, row.CustomerPhone)
			tempRow = append(tempRow, row.StudentUID)
			tempRow = append(tempRow, row.StudentName)
			tempRow = append(tempRow, row.BuyDate)
			tempRow = append(tempRow, row.ActivatedAtOld)
			tempRow = append(tempRow, originPhone)
			tempRow = append(tempRow, row.BuyActivateHourDiff)
			tempRow = append(tempRow, "是")
			if err = f.SetSheetRow(row.AgencyName, addr, &tempRow); err != nil {
				log.Printf("出错了：%v", err)
			}
			//
			tempRow[0] = rk - 1
			if err = f.SetSheetRow("Sheet1", "A"+strconv.Itoa(rk), &tempRow); err != nil {
				log.Printf("出错了：%v", err)
			}
			ExistAgencyMap[row.AgencyName] += 1
		}
		//防止循环过快
		time.Sleep(50 * time.Millisecond)
	}

	if err = f.SaveAs(name1); err != nil {
		log.Printf("出错了：%v", err)
	}
}

func getSuitData() ([]TerminalData, error) {
	var data []TerminalData
	err := DB1.Table("warranty as w").
		Select(
			"CONCAT(`rp`.`region_name`, `rc`.`region_name`) as zone",
			"`e`.`name` `terminal`",
			"`e`.`code`",
			"`e`.`address`",
			"`e`.`manager`",
			"`e`.`phone`",
			"`w`.`model`",
			"`w`.`barcode`",
			"`w`.`number`",
			"`w`.`customer_name`",
			"`w`.`customer_phone`",
			"`w`.`student_uid`",
			"`w`.`student_name`",
			"w.activated_id",
			"a.name as agency_name",
			"DATE_FORMAT(`w`.`buy_date`, '%Y-%m-%d %H:%i:%S') as buy_date",
			"DATE_FORMAT(`w`.`activated_at_old`, '%Y-%m-%d %H:%i:%S') as activated_at_old",
			"TIMESTAMPDIFF(HOUR, `w`.`buy_date`, `w`.`activated_at_old`) as buy_activate_hour_diff",
		).
		Joins("left join warranty_return as w1 on w.barcode = w1.barcode and w1.return_at > ? and w1.return_at < ?", "2024-08-01 00:00:00", "2024-08-10 00:00:00").
		Joins("left join endpoint as e on w.endpoint = e.id").
		Joins("left join agency as a on e.top_agency = a.id").
		Joins("left join region as rp on e.province = rp.region_id").
		Joins("left join region as rc on e.city = rc.region_id").
		Where("w.status = 1").
		//Where("w1.id is null").
		Where("a.channel = ?", "agency").
		Where("w.buy_date > ?", start).
		Where("w.buy_date < ?", end).
		Where("w.model in (?)", model).
		Where("TIMESTAMPDIFF(SECOND, w.buy_date, w.activated_at_old) >= -7200").
		Where("TIMESTAMPDIFF(SECOND, w.buy_date, w.activated_at_old) <= 7200").
		Group("w.barcode").
		Order("w.buy_date").Scan(&data).Error
	if err != nil {
		fmt.Println(err)
	}
	return data, err
}

func getOriginData() ([]TerminalData, error) {
	var data []TerminalData
	err := DB1.Table("warranty as w").
		Select(
			"CONCAT(`rp`.`region_name`, `rc`.`region_name`) as zone",
			"`e`.`name` `terminal`",
			"`e`.`code`",
			"`e`.`address`",
			"`e`.`manager`",
			"`e`.`phone`",
			"`w`.`model`",
			"`w`.`barcode`",
			"`w`.`number`",
			"`w`.`customer_name`",
			"`w`.`customer_phone`",
			"`w`.`student_uid`",
			"`w`.`student_name`",
			"w.activated_id",
			"w1.return_at",
			"a.name as agency_name",
			"DATE_FORMAT(`w`.`buy_date`, '%Y-%m-%d %H:%i:%S') as buy_date",
			"DATE_FORMAT(`w`.`activated_at_old`, '%Y-%m-%d %H:%i:%S') as activated_at_old",
			"TIMESTAMPDIFF(HOUR, `w`.`buy_date`, `w`.`activated_at_old`) as buy_activate_hour_diff",
		).
		Joins("left join warranty_return as w1 on w.barcode = w1.barcode").
		Joins("left join endpoint as e on w.endpoint = e.id").
		Joins("left join agency as a on e.top_agency = a.id").
		Joins("left join region as rp on e.province = rp.region_id").
		Joins("left join region as rc on e.city = rc.region_id").
		Where("w.status = 1").
		Where("a.channel = ?", "agency").
		Where("w.buy_date > ?", start).
		Where("w.buy_date < ?", end).
		Where("w.model in (?)", model).
		Group("w.barcode").
		Order("w.buy_date").Scan(&data).Error
	if err != nil {
		fmt.Println(err)
	}
	return data, err
}

func importOriginData() {
	var (
		title = []string{
			"序号",
			"区域",
			"终端",
			"终端代码",
			"门店地址",
			"店长名称",
			"店长电话",
			"机型",
			"S/N码",
			"序列号",
			"客户姓名",
			"联系电话",
			"学生UID",
			"学生姓名",
			"购买时间",
			"激活时间",
			"激活手机号码",
			"购买激活小时差",
			"购买激活手机号是否一致",
			"退机时间",
		}
		titleStyle     int
		addr           string
		err            error
		ExistAgency    []string
		ExistAgencyMap = make(map[string]int)
	)
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			log.Printf("出错了：%v", err)
		}
	}()
	// 标题行样式
	if titleStyle, err = f.NewStyle(&excelize.Style{
		Font:      &excelize.Font{Color: "1f7f3b", Bold: true, Family: "Microsoft YaHei"},
		Fill:      excelize.Fill{Type: "pattern", Color: []string{"E6F4EA"}, Pattern: 1},
		Alignment: &excelize.Alignment{Vertical: "center"},
		Border: []excelize.Border{
			{Type: "top", Style: 2, Color: "1f7f3b"},
			{Type: "bottom", Style: 1, Color: "1f7f3b"},
			{Type: "left", Style: 1, Color: "1f7f3b"}},
	}); err != nil {
		log.Printf("出错了：%v", err)
	}
	//设置头部
	if err = f.SetSheetRow("Sheet1", "A1", &title); err != nil {
		log.Printf("出错了：%v", err)
	}
	if err = f.SetRowHeight("Sheet1", 1, 30); err != nil {
		log.Printf("出错了：%v", err)
	}
	// 设置标题行样式
	if err = f.SetCellStyle("Sheet1", "A1", "T1", titleStyle); err != nil {
		log.Printf("出错了：%v", err)
	}
	// 设置列宽
	if err = f.SetColWidth("Sheet1", "A", "T", 20); err != nil {
		log.Printf("出错了：%v", err)
	}
	data1, _ := getOriginData()
	for rk, row := range data1 {
		//查出手机号是否一致
		originPhone := ""
		originPhoneStr := "否"

		err = DB2.Table("ac_devices_uniq").Where("id = ?", row.ActivatedId).Select("origin").Scan(&originPhone).Error
		if err != nil {
			fmt.Println(err)
			return
		}
		if originPhone == row.CustomerPhone {
			originPhoneStr = "是"
		}

		sort.Strings(ExistAgency)
		_, found := slices.BinarySearch(ExistAgency, row.AgencyName)
		if found == false {
			//新建工作表
			_, err = f.NewSheet(row.AgencyName)
			if err != nil {
				fmt.Println(err)
				return
			}
			//设置头部
			if err = f.SetSheetRow(row.AgencyName, "A1", &title); err != nil {
				log.Printf("出错了：%v", err)
			}
			if err = f.SetRowHeight(row.AgencyName, 1, 30); err != nil {
				log.Printf("出错了：%v", err)
			}
			// 设置标题行样式
			if err = f.SetCellStyle(row.AgencyName, "A1", "T1", titleStyle); err != nil {
				log.Printf("出错了：%v", err)
			}
			// 设置列宽
			if err = f.SetColWidth(row.AgencyName, "A", "T", 20); err != nil {
				log.Printf("出错了：%v", err)
			}

			//赋值
			if addr, err = excelize.JoinCellName("A", 2); err != nil {
				if err != nil {
					log.Printf("出错了：%v", err)
				}
			}
			var tempRow []interface{}
			tempRow = append(tempRow, 1)
			tempRow = append(tempRow, row.Zone)
			tempRow = append(tempRow, row.Terminal)
			tempRow = append(tempRow, row.Code)
			tempRow = append(tempRow, row.Address)
			tempRow = append(tempRow, row.Manager)
			tempRow = append(tempRow, row.Phone)
			tempRow = append(tempRow, row.Model)
			tempRow = append(tempRow, row.Barcode)
			tempRow = append(tempRow, row.Number)
			tempRow = append(tempRow, row.CustomerName)
			tempRow = append(tempRow, row.CustomerPhone)
			tempRow = append(tempRow, row.StudentUID)
			tempRow = append(tempRow, row.StudentName)
			tempRow = append(tempRow, row.BuyDate)
			tempRow = append(tempRow, row.ActivatedAtOld)
			tempRow = append(tempRow, originPhone)
			tempRow = append(tempRow, row.BuyActivateHourDiff)
			tempRow = append(tempRow, originPhoneStr)
			tempRow = append(tempRow, row.ReturnAt)
			if err = f.SetSheetRow(row.AgencyName, addr, &tempRow); err != nil {
				log.Printf("出错了：%v", err)
			}
			tempRow[0] = rk
			if err = f.SetSheetRow("Sheet1", "A"+strconv.Itoa(rk+2), &tempRow); err != nil {
				log.Printf("出错了：%v", err)
			}
			ExistAgency = append(ExistAgency, row.AgencyName)
			ExistAgencyMap[row.AgencyName] = 2

		} else {
			if addr, err = excelize.JoinCellName("A", ExistAgencyMap[row.AgencyName]+1); err != nil {
				if err != nil {
					log.Printf("出错了：%v", err)
				}
			}
			var tempRow []interface{}
			tempRow = append(tempRow, ExistAgencyMap[row.AgencyName])
			tempRow = append(tempRow, row.Zone)
			tempRow = append(tempRow, row.Terminal)
			tempRow = append(tempRow, row.Code)
			tempRow = append(tempRow, row.Address)
			tempRow = append(tempRow, row.Manager)
			tempRow = append(tempRow, row.Phone)
			tempRow = append(tempRow, row.Model)
			tempRow = append(tempRow, row.Barcode)
			tempRow = append(tempRow, row.Number)
			tempRow = append(tempRow, row.CustomerName)
			tempRow = append(tempRow, row.CustomerPhone)
			tempRow = append(tempRow, row.StudentUID)
			tempRow = append(tempRow, row.StudentName)
			tempRow = append(tempRow, row.BuyDate)
			tempRow = append(tempRow, row.ActivatedAtOld)
			tempRow = append(tempRow, originPhone)
			tempRow = append(tempRow, row.BuyActivateHourDiff)
			tempRow = append(tempRow, originPhoneStr)
			tempRow = append(tempRow, row.ReturnAt)
			if err = f.SetSheetRow(row.AgencyName, addr, &tempRow); err != nil {
				log.Printf("出错了：%v", err)
			}
			tempRow[0] = rk
			if err = f.SetSheetRow("Sheet1", "A"+strconv.Itoa(rk+2), &tempRow); err != nil {
				log.Printf("出错了：%v", err)
			}
			ExistAgencyMap[row.AgencyName] += 1
		}
		//防止循环过快
		time.Sleep(50 * time.Millisecond)
	}
	if err != nil {
		log.Printf("出错了：%v", err)
	}
	if err = f.SaveAs(name2); err != nil {
		log.Printf("出错了：%v", err)
	}
}

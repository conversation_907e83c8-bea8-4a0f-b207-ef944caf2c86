package main

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/xuri/excelize/v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"strconv"
	"strings"
	"time"
)

/*var host = "https://api-yxtest.readboy.com/"
var username = "yxmanager"
var password = "123456"*/

var host = "https://api-yx.readboy.com/"
var username = "stat"
var password = "0b6ca943"

var DB1 *gorm.DB

var DB2 *gorm.DB
var ucDB *gorm.DB
var AccessToken = ""

var agencyEndpointMap = map[string]int{
	"494": 4854,
	"627": 8478,
	"621": 8314,
	"593": 8583,
	"720": 11139,
}

type AcDevicesUniq struct {
	ID         int    `gorm:"column:id;primary_key" json:"id"`
	Series     int8   `gorm:"column:series" json:"series"`
	ModelID    int    `gorm:"column:model_id" json:"model_id"`
	Model      string `gorm:"column:model" json:"model"`
	NavModel   string `gorm:"column:nav_model" json:"nav_model"`
	Uniqno     string `gorm:"column:uniqno" json:"uniqno"`
	Barcode    string `gorm:"column:barcode" json:"barcode"`
	Number     string `gorm:"column:number" json:"number"`
	IMEI       string `gorm:"column:imei" json:"imei"`
	Province   string `gorm:"column:province" json:"province"`
	City       string `gorm:"column:city" json:"city"`
	District   string `gorm:"column:district" json:"district"`
	Address    string `gorm:"column:address" json:"address"`
	Adcode     string `gorm:"column:adcode" json:"adcode"`
	Area       string `gorm:"column:area" json:"area"`
	Subarea    string `gorm:"column:subarea" json:"subarea"`
	AgencyID   uint   `gorm:"column:agency_id" json:"agency_id"`
	AgencyName string `gorm:"column:agency_name" json:"agency_name"`
	MesExists  int8   `gorm:"column:mes_exists" json:"mes_exists"`
	CustCode   string `gorm:"column:cust_code" json:"cust_code"`
	CustName   string `gorm:"column:cust_name" json:"cust_name"`
	Channel    string `gorm:"column:channel" json:"channel"`
	Exchanged  int8   `gorm:"column:exchanged" json:"exchanged"`
	Createat   string `gorm:"column:createat" json:"createat"`
	Status     int8   `gorm:"column:status" json:"status"`
	DeletedAt  string `gorm:"column:deleted_at" json:"deleted_at"`
	Bindat     string `gorm:"column:bindat" json:"bindat"`
	Location   string `gorm:"column:location" json:"location"`
	IP         string `gorm:"column:ip" json:"ip"`
	Origin     string `gorm:"column:origin" json:"origin"`
	UID        int    `gorm:"column:uid" json:"uid"`
	Did        int    `gorm:"column:did" json:"did"`
}

type OutData struct {
	AgencyChannel string
	AgencyID      string
	AgencyName    string
	Barcode       string
	BillDate      string
	CustChannel   string
	CustCode      string
	CustName      string
	GroupCode     string
	GroupName     string
}
type EndpointView struct {
	EndpointID   int
	EndpointName string
	UserID       string
	UserName     string
}

func main() {
	//return
	//fmt.Println(getToken())
	var err error
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second, // 慢 SQL 阈值
			LogLevel:                  logger.Info, // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略ErrRecordNotFound（记录未找到）错误
			Colorful:                  true,        // 禁用彩色打印
		},
	)
	dsn := "yxphp:cJvkmFOmWQ3y3Etk@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare"
	DB1, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}
	sqlDB1, _ := DB1.DB()
	sqlDB1.SetMaxIdleConns(2)
	sqlDB1.SetMaxOpenConns(2)

	dsn2 := "yxphp:cJvkmFOmWQ3y3Etk@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare_data"
	DB2, err = gorm.Open(mysql.Open(dsn2), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}
	sqlDB2, _ := DB2.DB()
	sqlDB2.SetMaxIdleConns(2)
	sqlDB2.SetMaxOpenConns(2)

	dsn3 := "user2015_ac:readboy@tcp(rds49edhgd0910y6ok82public.mysql.rds.aliyuncs.com)/ac_center"
	ucDB, err = gorm.Open(mysql.Open(dsn3), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}
	sqlDB3, _ := ucDB.DB()
	sqlDB3.SetMaxIdleConns(2)
	sqlDB3.SetMaxOpenConns(2)

	//读取excel
	f, err := excelize.OpenFile("v20.xlsx")
	if err != nil {
		fmt.Println(err)
		return
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	// 获取 Sheet1 上所有单元格
	rows, err := f.GetRows("Sheet1")
	if err != nil {
		fmt.Println(err)
		return
	}
	for rk, row := range rows {
		if rk == 0 {
			f.SetColWidth("Sheet1", "K", "K", 25)
			f.SetColWidth("Sheet1", "L", "L", 45)
			f.SetCellValue("Sheet1", "K"+strconv.Itoa(rk+1), "保卡生成结果")
			continue
		}

		msg, query := addWarranty(row[0])
		jsonData, _ := json.Marshal(query)
		err = f.SetCellValue("Sheet1", "K"+strconv.Itoa(rk+1), msg)
		err = f.SetCellValue("Sheet1", "L"+strconv.Itoa(rk+1), jsonData)
		if err != nil {
			fmt.Println(err)
		}
		time.Sleep(1000 * time.Millisecond)
	}
	err = f.Save()
	if err != nil {
		fmt.Println(err)
	}
}

func addWarranty(barcode string) (string, map[string]string) {
	url := host + "api/v1/warranty"

	token := getToken()
	deviceID, sn, t := getSn()
	var result map[string]interface{}
	queryParams := make(map[string]string)
	queryParams["device_id"] = deviceID
	queryParams["sn"] = sn
	queryParams["t"] = t
	queryParams["access_token"] = token
	//queryParams["access_token"] = "81c26f46fe193b61daca0b4329f7ed23"

	//获取激活的数据
	var data AcDevicesUniq
	err := DB2.Table("rbcare_data.ac_devices_uniq").
		Where("barcode = ?", barcode).
		Where("status = 0").First(&data).Error
	if err != nil {
		fmt.Println(err)
		return "激活数据不存在:" + err.Error(), queryParams
	}
	dataStatus := getWarranty(barcode)
	if dataStatus == false {
		return "保卡数据不存在", queryParams
	}
	fmt.Println("激活数据：", data)
	//出库数据
	outData, err := getOutData(barcode)
	if err != nil && outData.AgencyID == "0" && outData.AgencyName == "" {
		return "出库数据有问题：" + err.Error(), queryParams
	}
	fmt.Println("c出库数据：", outData)
	// 判断是否退货
	returnBool, err := getReturnData(barcode)
	if returnBool {
		return "已退货", queryParams
	}
	//终端数据
	eInfo, err := getUerEndPointView(agencyEndpointMap[outData.AgencyID])
	if err != nil {
		return "终端查询错误：" + err.Error(), queryParams
	}
	fmt.Println("终端信息：", eInfo)

	//保卡参数平板
	queryParams["barcode"] = barcode

	loc, _ := time.LoadLocation("Asia/Shanghai")
	parsedTime, _ := time.ParseInLocation(time.DateTime, data.Createat, loc)
	timestamp := parsedTime.Unix()
	fmt.Println("时间戳===================================")
	fmt.Println(timestamp)
	queryParams["date"] = strconv.Itoa(int(timestamp))
	queryParams["user_id"] = eInfo.UserID
	queryParams["salesman"] = eInfo.UserName
	queryParams["phone"] = data.Origin
	queryParams["type"] = "2"
	queryParams["notify"] = "0"
	//获取用户信息
	uInfo, _ := getAcMemberData(data.UID)
	if uInfo["uid"] != nil {
		if uInfo["mobile"] != nil {
			queryParams["phone"] = uInfo["mobile"].(string)
		}

		if uInfo["realname"] != nil {
			queryParams["student_name"] = uInfo["realname"].(string)
		}
	}
	fmt.Println(queryParams)

	resp, err := resty.New().R().SetQueryParams(queryParams).SetResult(&result).Post(url)
	if err != nil {
		fmt.Println(err)
		return "保卡接口请求失败：" + err.Error(), queryParams
	}
	queryParams["date_str"] = data.Createat
	fmt.Println(resp)
	if resp.IsSuccess() {
		return result["msg"].(string), queryParams
	}
	return "未知状态", queryParams
}

func getMD5(input string) string {
	hash := md5.Sum([]byte(input))
	return hex.EncodeToString(hash[:])
}

func getToken() string {
	if AccessToken != "" {
		return AccessToken
	}

	url := host + "api/v2/login"

	deviceID, sn, t := getSn()
	var result map[string]interface{}
	queryParams := make(map[string]string)
	queryParams["device_id"] = deviceID
	queryParams["sn"] = sn
	queryParams["t"] = t
	queryParams["username"] = username
	queryParams["password"] = password

	resp, err := resty.New().R().SetQueryParams(queryParams).SetResult(&result).Post(url)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(resp)
	if resp.IsSuccess() {
		userData := result["data"].(map[string]interface{})
		AccessToken = userData["access_token"].(string)
		return AccessToken
	}
	return ""
}

func getSn() (string, string, string) {
	model := ""
	uniqueID := ""
	appID := "yxlogin.readboy.com"
	appVersion := ""
	deviceID := strings.Join([]string{model, uniqueID, appID, appVersion}, "/")
	t := fmt.Sprint(time.Now().Unix())
	appSecret := "31879b82704954028802c9325bbcd11c"
	sn := getMD5(deviceID + appSecret + t)

	return deviceID, sn, t
}

func getOutData(barcode string) (OutData, error) {
	//出库信息
	var outData OutData
	err := DB2.Table("v3_outstock_view").
		Select(`barcode`,
			`bill_date`,
			"IF(`cust_code_new` IS NULL, `cust_code`, `cust_code_new`) AS `cust_code`",
			"IF(`cust_code_new` IS NULL, `cust_name`, `cust_name_new`) AS `cust_name`",
			"IF(`cust_code_new` IS NULL, `cust_channel`, `cust_channel_new`) AS `cust_channel`",
			"IF(`cust_code_new` IS NULL, `group_code`, `group_code_new`) AS `group_code`",
			"IF(`cust_code_new` IS NULL, `group_name`, `group_name_new`) AS `group_name`",
			"IF(`cust_code_new` IS NULL, `agency_id`, `agency_id_new`) AS `agency_id`",
			"IF(`cust_code_new` IS NULL, `agency_name`, `agency_name_new`) AS `agency_name`",
			"IF(`cust_code_new` IS NULL, `agency_channel`, `agency_channel_new`) AS `agency_channel`").
		Where("barcode = ?", barcode).
		Where("status = 1").
		Order("id desc").
		Limit(1).
		Find(&outData).Error
	if err != nil {
		fmt.Println(err)
	}
	return outData, err
}

func getReturnData(barcode string) (bool, error) {
	var id int
	err := DB2.Table("rbcare_data.v3_returnstock").
		Where("barcode = ?", barcode).
		Where("status = 1").Select("id").Scan(&id).Error
	if err != nil || id == 0 {
		fmt.Println(err)
		return false, err
	}
	fmt.Println("====================")
	fmt.Println("退货数据：", id)
	return true, nil
}

func getUerEndPointView(endpointID int) (EndpointView, error) {
	var data EndpointView
	err := DB1.Table("user_endpoint_view").
		Select("endpoint_id", "endpoint_name", "user_id", "user_name").
		Where("top_agency_channel in (?)", []string{"e_commerce", "ebag", "operator"}).
		Where("endpoint_status = 1").
		Where("role_slug = ?", "endpoint").
		Where("user_status = 1").
		Where("user_role = ?", "manager").
		Where("user_type = ?", "user").
		Where("endpoint_id = ?", endpointID).
		Limit(1).Find(&data).Error
	if err != nil {
		fmt.Println(err)
	}
	return data, err
}

func getAcMemberData(uid int) (map[string]interface{}, error) {
	var uData map[string]interface{}
	err := ucDB.Table("ac_members_view").Where("uid = ?", uid).Find(&uData).Error
	fmt.Println("顾客数据：", uData)
	return uData, err
}

type Warranty struct {
	Id             int
	ActivatedAtOld string
}

func getWarranty(barcode string) bool {
	var data Warranty
	err := DB1.Table("warranty").Select("id", "activated_at_old").
		Where("barcode = ?", barcode).Order("id desc").
		Limit(1).Find(&data).Error
	if err != nil {
		fmt.Println(err)
		return false
	}
	fmt.Println("保卡数据：======", data)
	if data.Id != 0 && data.ActivatedAtOld != "" {
		return true
	}
	return false
}

SELECT
    CONCAT(`rp`.`region_name`, `rc`.`region_name`) `区域`,
    `e`.`name` `终端`,
    `e`.`code` `终端代码`,
    `e`.`address` `门店地址`,
    `e`.`manager` `店长名称`,
    `e`.`phone` `店长电话`,
    `w`.`model` `机型`,
    `w`.`barcode` `S/N码`,
    `w`.`number` `序列号`,
    `w`.`customer_name` `客户姓名`,
    `w`.`customer_phone` `联系电话`,
    `w`.`student_uid` `学生UID`,
    `w`.`student_name` `学生姓名`,
    DATE_FORMAT(`w`.`buy_date`, '%Y-%m-%d %H:%i:%S') `购买时间`,
    DATE_FORMAT(`w`.`activated_at_old`, '%Y-%m-%d %H:%i:%S') `激活时间`,
    `adu`.`origin` AS `激活手机号码`,
    TIMESTAMPDIFF(HOUR, `w`.`buy_date`, `w`.`activated_at_old`) `购买激活小时差`,
    IF (`w`.`customer_phone` = `adu`.`origin`, '是', '否') `购买激活手机号码是否一致`
FROM
    `warranty` `w`
LEFT JOIN
    `warranty_return` `w1`
ON
    `w`.`barcode` = `w1`.`barcode`
LEFT JOIN
    `endpoint` `e`
ON
    `w`.`endpoint` = `e`.`id`
LEFT JOIN
    `agency` `ta`
ON
    `e`.`top_agency` = `ta`.`id`
LEFT JOIN
    `region` `rp`
ON
    `e`.`province` = `rp`.`region_id`
LEFT JOIN
    `region` `rc`
ON
    `e`.`city` = `rc`.`region_id`
LEFT JOIN
        `rbcare_data`.`ac_devices_uniq` `adu`
ON
    `w`.`activated_id` = `adu`.`id`
WHERE
    `w`.`status` = 1
    AND
    `w`.`model` IN ('C60','F16Pro','F16','C35','C28','C13pro','C26Pro', 'C26', 'C80Pro','C80','C70Pro','C70')
    AND
    `ta`.`channel` = 'agency'
    AND
    `w`.`buy_date` >= CONCAT('2024-06-16', ' 00:00:00')
    AND
    `w`.`buy_date` < CONCAT('2024-06-16', ' 23:59:59')
    AND
    TIMESTAMPDIFF(SECOND, `w`.`buy_date`, `w`.`activated_at_old`) >= -7200
    AND
    TIMESTAMPDIFF(SECOND, `w`.`buy_date`, `w`.`activated_at_old`) <= 7200
    AND
    `w`.`customer_phone` = `adu`.`origin`
    AND `w1`.`id` IS null
ORDER BY
    `w`.`buy_date`
;
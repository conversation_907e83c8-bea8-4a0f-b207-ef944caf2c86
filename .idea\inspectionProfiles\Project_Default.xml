<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="25">
            <item index="0" class="java.lang.String" itemvalue="pycurl" />
            <item index="1" class="java.lang.String" itemvalue="bce_python_sdk" />
            <item index="2" class="java.lang.String" itemvalue="arrow" />
            <item index="3" class="java.lang.String" itemvalue="python_json_logger" />
            <item index="4" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="5" class="java.lang.String" itemvalue="numpy" />
            <item index="6" class="java.lang.String" itemvalue="requests" />
            <item index="7" class="java.lang.String" itemvalue="redis" />
            <item index="8" class="java.lang.String" itemvalue="python_dateutil" />
            <item index="9" class="java.lang.String" itemvalue="pytest-tornado" />
            <item index="10" class="java.lang.String" itemvalue="aiomysql" />
            <item index="11" class="java.lang.String" itemvalue="tornado" />
            <item index="12" class="java.lang.String" itemvalue="odps" />
            <item index="13" class="java.lang.String" itemvalue="pyDes" />
            <item index="14" class="java.lang.String" itemvalue="Tornado_MySQL" />
            <item index="15" class="java.lang.String" itemvalue="bcrypt" />
            <item index="16" class="java.lang.String" itemvalue="simplejson" />
            <item index="17" class="java.lang.String" itemvalue="hiredis" />
            <item index="18" class="java.lang.String" itemvalue="safe_logger" />
            <item index="19" class="java.lang.String" itemvalue="phone" />
            <item index="20" class="java.lang.String" itemvalue="oss2" />
            <item index="21" class="java.lang.String" itemvalue="wechatpy" />
            <item index="22" class="java.lang.String" itemvalue="cachetools" />
            <item index="23" class="java.lang.String" itemvalue="mq_http_sdk" />
            <item index="24" class="java.lang.String" itemvalue="openpyxl" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N802" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>
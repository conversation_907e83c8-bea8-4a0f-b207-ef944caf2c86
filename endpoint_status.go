package main

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/xuri/excelize/v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type Endpoint struct {
	Id     string `json:"id"`
	Name   string `json:"name"`
	Code   string `json:"code"`
	Status string `json:"status"`
}

var StatusDB *gorm.DB

func main() {
	fmt.Println("开始更新终端状态...")
	RunStatusUpdate()
	fmt.Println("终端状态更新完成！")

	fmt.Println("开始将状态从0更新为1...")
	updateStatus()
	fmt.Println("状态更新完成！")
}

func RunStatusUpdate() {
	dsn := "yxphp:cJvkmFOmWQ3y3Etk@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare"
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second, // 慢 SQL 阈值
			LogLevel:                  logger.Info, // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略ErrRecordNotFound（记录未找到）错误
			Colorful:                  true,        // 禁用彩色打印
		},
	)
	var err error
	StatusDB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}
	sqlDB, _ := StatusDB.DB()
	sqlDB.SetMaxIdleConns(1)
	sqlDB.SetMaxOpenConns(1)
	getStatus()
}

// getStatus 读取Excel文件，获取第二列的code，查询数据库中的状态，并将状态填回Excel表第六列
func getStatus() {
	// 读取Excel文件
	f, err := excelize.OpenFile("终端5000家(1).xlsx")
	if err != nil {
		fmt.Println("打开Excel文件出错:", err)
		return
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println("关闭Excel文件出错:", err)
		}
	}()

	// 获取工作表中的所有行
	sheetName := f.GetSheetName(0) // 获取第一个工作表的名称
	rows, err := f.GetRows(sheetName)
	if err != nil {
		fmt.Println("获取Excel行数据出错:", err)
		return
	}

	// 处理每一行数据
	for i, row := range rows {
		// 跳过标题行
		if i == 0 {
			// 确保第六列有标题
			if len(row) < 6 {
				// 如果列数不足6列，添加空列直到第6列
				for j := len(row); j < 6; j++ {
					// 将列索引转换为Excel列字母（A, B, C, ...）
					colName, err := excelize.ColumnNumberToName(j + 1) // +1 因为Excel列从1开始
					if err != nil {
						fmt.Printf("列索引转换出错: %v\n", err)
						continue
					}
					err = f.SetCellValue(sheetName, colName+strconv.Itoa(i+1), "")
				}
			}
			// 设置第六列标题为"状态"
			err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i+1), "状态")
			if err != nil {
				fmt.Println("设置状态列标题出错:", err)
			}
			continue
		}

		// 确保行至少有2列数据
		if len(row) < 2 || row[1] == "" {
			fmt.Printf("第%d行没有code数据，跳过\n", i+1)
			continue
		}

		// 获取第二列的code
		code := row[1]

		// 查询数据库获取状态
		var endpoint Endpoint
		err = StatusDB.Table("endpoint").Select("status").Where("code = ?", code).First(&endpoint).Error
		if err != nil {
			fmt.Printf("查询code=%s的状态出错: %v\n", code, err)
			// 设置状态为"未找到"
			err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i+1), "未找到")
			if err != nil {
				fmt.Printf("设置第%d行状态出错: %v\n", i+1, err)
			}
			continue
		}

		// 将状态写入第六列
		err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i+1), endpoint.Status)
		if err != nil {
			fmt.Printf("设置第%d行状态出错: %v\n", i+1, err)
		}

		// 每处理100行输出一次进度
		if i%100 == 0 {
			fmt.Printf("已处理 %d 行数据\n", i)
		}

		// 短暂延迟，避免数据库压力过大
		time.Sleep(10 * time.Millisecond)
	}

	// 保存修改后的Excel文件
	err = f.SaveAs("终端5000家_状态更新.xlsx")
	if err != nil {
		fmt.Println("保存Excel文件出错:", err)
		return
	}

	fmt.Println("状态更新完成，已保存为 终端5000家_状态更新.xlsx")
}

// updateStatus 读取Excel文件的第六列，如果状态是0就修改成为1，并且在第七列给出更新的结果
func updateStatus() {
	// 读取Excel文件
	f, err := excelize.OpenFile("终端5000家_状态更新.xlsx")
	if err != nil {
		fmt.Println("打开Excel文件出错:", err)
		return
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println("关闭Excel文件出错:", err)
		}
	}()

	// 获取工作表中的所有行
	sheetName := f.GetSheetName(0) // 获取第一个工作表的名称
	rows, err := f.GetRows(sheetName)
	if err != nil {
		fmt.Println("获取Excel行数据出错:", err)
		return
	}

	// 处理每一行数据
	for i, row := range rows {
		// 跳过标题行
		if i == 0 {
			// 确保第七列有标题
			if len(row) < 7 {
				// 如果列数不足7列，添加空列直到第7列
				for j := len(row); j < 7; j++ {
					// 将列索引转换为Excel列字母（A, B, C, ...）
					colName, err := excelize.ColumnNumberToName(j + 1) // +1 因为Excel列从1开始
					if err != nil {
						fmt.Printf("列索引转换出错: %v\n", err)
						continue
					}
					err = f.SetCellValue(sheetName, colName+strconv.Itoa(i+1), "")
				}
			}
			// 设置第七列标题为"更新结果"
			err = f.SetCellValue(sheetName, "G"+strconv.Itoa(i+1), "更新结果")
			if err != nil {
				fmt.Println("设置更新结果列标题出错:", err)
			}
			continue
		}

		// 确保行至少有6列数据（包含状态列）
		if len(row) < 6 || row[5] == "" {
			fmt.Printf("第%d行没有状态数据，跳过\n", i+1)
			continue
		}

		// 获取第二列的code和第六列的状态
		code := row[1]
		status := row[5]

		// 如果状态是0，则更新为1
		if status == "0" {
			// 更新数据库中的状态
			result := StatusDB.Table("endpoint").Where("code = ?", code).Update("status", "1")
			if result.Error != nil {
				fmt.Printf("更新code=%s的状态出错: %v\n", code, result.Error)
				// 记录更新失败
				err = f.SetCellValue(sheetName, "G"+strconv.Itoa(i+1), "更新失败: "+result.Error.Error())
				if err != nil {
					fmt.Printf("设置第%d行更新结果出错: %v\n", i+1, err)
				}
				continue
			}

			// 检查是否有记录被更新
			if result.RowsAffected == 0 {
				fmt.Printf("没有找到code=%s的记录\n", code)
				err = f.SetCellValue(sheetName, "G"+strconv.Itoa(i+1), "未找到记录")
				if err != nil {
					fmt.Printf("设置第%d行更新结果出错: %v\n", i+1, err)
				}
				continue
			}

			// 更新Excel中的状态
			err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i+1), "1")
			if err != nil {
				fmt.Printf("更新第%d行状态出错: %v\n", i+1, err)
			}

			// 记录更新成功
			err = f.SetCellValue(sheetName, "G"+strconv.Itoa(i+1), "已更新")
			if err != nil {
				fmt.Printf("设置第%d行更新结果出错: %v\n", i+1, err)
			}

			fmt.Printf("已将code=%s的状态从0更新为1\n", code)
		} else {
			// 状态不是0，记录无需更新
			err = f.SetCellValue(sheetName, "G"+strconv.Itoa(i+1), "无需更新")
			if err != nil {
				fmt.Printf("设置第%d行更新结果出错: %v\n", i+1, err)
			}
		}

		// 每处理100行输出一次进度
		if i%100 == 0 {
			fmt.Printf("已处理 %d 行数据\n", i)
		}

		// 短暂延迟，避免数据库压力过大
		time.Sleep(10 * time.Millisecond)
	}

	// 保存修改后的Excel文件
	err = f.SaveAs("终端5000家_状态已更新.xlsx")
	if err != nil {
		fmt.Println("保存Excel文件出错:", err)
		return
	}

	fmt.Println("状态更新完成，已保存为 终端5000家_状态已更新.xlsx")
}

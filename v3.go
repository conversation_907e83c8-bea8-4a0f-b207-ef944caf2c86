package main

import (
	"fmt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"strings"
	"time"
)

var DB1 *gorm.DB
var DB2 *gorm.DB

func main() {
	var err error
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second, // 慢 SQL 阈值
			LogLevel:                  logger.Info, // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略ErrRecordNotFound（记录未找到）错误
			Colorful:                  true,        // 禁用彩色打印
		},
	)
	//这个是mes在阿里云上的库，内网的库实时同步到外网上，账号只有只读权限
	dsn := "rbproduction:BVBTdShnJ6@tcp(rm-wz9i89w1m7788168oyo.mysql.rds.aliyuncs.com)/mes"
	DB1, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}
	sqlDB1, _ := DB1.DB()
	sqlDB1.SetMaxIdleConns(1)
	sqlDB1.SetMaxOpenConns(1)

	dsn2 := "yxphp:cJvkmFOmWQ3y3Etk@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare"
	DB2, err = gorm.Open(mysql.Open(dsn2), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}
	sqlDB2, _ := DB2.DB()
	sqlDB2.SetMaxIdleConns(1)
	sqlDB2.SetMaxOpenConns(1)
	dealData2()
}

func dealData() {
	var strSlice []string
	err := DB1.Table("v3_barcode").Where("model=?", "C28G").Pluck("barcode", &strSlice).Error
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println("count:", len(strSlice))
	for i, s := range strSlice {
		strSlice[i] = "'" + s + "'"
	}
	result := strings.Join(strSlice, ",")

	// 打印结果
	fmt.Println(result) // 输出: apple, banana, cherry
}

type Warranty struct {
	Model   string
	Barcode string
	ModelId int
}

func dealData2() {
	var datas []Warranty
	err := DB2.Table("warranty").Where("activated_at_old > ?", "'2024-11-15 00:00:00'").Select("model,barcode,model_id").Limit(2000).Scan(&datas).Error
	if err != nil {
		fmt.Println(err)
	}
	var needFix []string
	for _, data := range datas {
		//fmt.Println("warranty:", data)
		actualModel := ""
		err = DB1.Table("v3_barcode").Where("barcode=?", data.Barcode).Select("model").Scan(&actualModel).Error
		if err != nil {
			fmt.Println(err)
			return
		}

		printModel := ""
		err = DB1.Table("v3_device_type").Where("name=?", actualModel).Select("print_name").Scan(&printModel).Error
		if err != nil {
			fmt.Println(err)
			return
		}

		if data.Model != printModel {
			fmt.Println("机型，保卡-实际-打印", data.Model, "-", actualModel, "-", printModel)
			needFix = append(needFix, data.Barcode)
		}
	}
	fmt.Println(needFix)
}

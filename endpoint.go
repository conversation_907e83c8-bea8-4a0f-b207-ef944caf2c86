package main

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"log"
	"os"
	"slices"
	"sort"
	"strconv"
	"time"
)

// Endpoints 导出终端数据
type Endpoints struct {
	Id         string `json:"id"`
	Name       string `json:"name"`
	Address    string `json:"address"`
	Code       string `json:"code"`
	Agency     string `json:"agency"`
	Province   string `json:"province"`
	City       string `json:"city"`
	Type       string `json:"type"`
	Phone      string `json:"phone"`
	Manager    string `json:"manager"`
	CreatedAt  string `json:"created"`
	IsFortress int    `json:"is_fortress"`
}

var DB *gorm.DB

func main() {
	dsn := "yxphp:cJvkmFOmWQ3y3Etk@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare"
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second, // 慢 SQL 阈值
			LogLevel:                  logger.Info, // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略ErrRecordNotFound（记录未找到）错误
			Colorful:                  true,        // 禁用彩色打印
		},
	)
	var err error
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}
	sqlDB, _ := DB.DB()
	sqlDB.SetMaxIdleConns(1)
	sqlDB.SetMaxOpenConns(1)
	getExcel()
}

func getExcel() {

	var (
		title = []string{
			"ID",
			"终端编号",
			"终端名称",
			"终端地址",
			//"2024年销售额",
			"终端类型",
			"终端电话",
			"负责人",
			"总代",
			"省份",
			"城市",
			"创建时间",
		}
		sheet         = "Sheet1"
		titleStyle    int
		addr          string
		err           error
		ExistProvince []string
	)
	var ExistProvinceMap = make(map[string]int)

	var data []Endpoints
	err = DB.Table("endpoint as e").Select("e.name", "e.code", "e.address", "e.id", "a.name as agency",
		"rp.region_name as province", "rc.region_name as city", "e.created_at", "e.phone", "e.manager", "e.type", "e.is_fortress").
		Joins("left join agency as a on e.top_agency = a.id").
		Joins("left join region as rp on e.province = rp.region_id").
		Joins("left join region as rc on e.city = rc.region_id").Find(&data).Error
	//Where("e.type = ?", 4).
	//Where("a.channel = ?", "agency").
	//Where("e.status = 1").Find(&data).Error

	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			log.Printf("出错了：%v", err)
		}
	}()

	if err = f.SetSheetRow(sheet, "A1", &title); err != nil {
		log.Printf("出错了：%v", err)
	}

	// 标题行样式
	if titleStyle, err = f.NewStyle(&excelize.Style{
		Font:      &excelize.Font{Color: "1f7f3b", Bold: true, Family: "Microsoft YaHei"},
		Fill:      excelize.Fill{Type: "pattern", Color: []string{"E6F4EA"}, Pattern: 1},
		Alignment: &excelize.Alignment{Vertical: "center"},
		Border: []excelize.Border{
			{Type: "top", Style: 2, Color: "1f7f3b"},
			{Type: "bottom", Style: 1, Color: "1f7f3b"},
			{Type: "left", Style: 1, Color: "1f7f3b"}},
	}); err != nil {
		log.Printf("出错了：%v", err)
	}
	//sheet1
	//设置头部
	if err = f.SetSheetRow(sheet, "A1", &title); err != nil {
		log.Printf("出错了：%v", err)
	}
	if err = f.SetRowHeight(sheet, 1, 30); err != nil {
		log.Printf("出错了：%v", err)
	}
	// 设置标题行样式
	if err = f.SetCellStyle(sheet, "A1", "K1", titleStyle); err != nil {
		log.Printf("出错了：%v", err)
	}
	// 设置列宽
	if err = f.SetColWidth(sheet, "A", "A", 8); err != nil {
		log.Printf("出错了：%v", err)
	}
	if err = f.SetColWidth(sheet, "B", "B", 15); err != nil {
		log.Printf("出错了：%v", err)
	}
	if err = f.SetColWidth(sheet, "C", "C", 35); err != nil {
		log.Printf("出错了：%v", err)
	}
	if err = f.SetColWidth(sheet, "D", "D", 65); err != nil {
		log.Printf("出错了：%v", err)
	}
	if err = f.SetColWidth(sheet, "E", "K", 20); err != nil {
		log.Printf("出错了：%v", err)
	}
	// 按行赋值
	for rk, row := range data {
		sort.Strings(ExistProvince)
		_, found := slices.BinarySearch(ExistProvince, row.Province)
		//计算实销金额
		//var amount int
		//err = DB.Table("warranty").Where("endpoint = ?", row.Id).Where("realsale = ?", 1).
		//	Where("buy_date >= ?", "2024-01-01 00:00:00").
		//	Where("buy_date <= ?", "2024-10-01 00:00:00").
		//	Select("sum(customer_price) as amount").Scan(&amount).Error
		typeName := ""
		switch row.Type {
		case "1":
			typeName = "专柜"
		case "2":
			typeName = "运营商渠道"
		case "3":
			typeName = "专卖店"
		case "4":
			typeName = "城市综合体"
		case "5":
			typeName = "商超"
		case "6":
			typeName = "智习室"
		}
		if row.IsFortress == 1 {
			typeName = "城市综合体堡垒"
		}
		if found == false {
			//新建工作表
			_, err = f.NewSheet(row.Province)
			if err != nil {
				fmt.Println(err)
				return
			}
			//设置头部
			if err = f.SetSheetRow(row.Province, "A1", &title); err != nil {
				log.Printf("出错了：%v", err)
			}
			if err = f.SetRowHeight(row.Province, 1, 30); err != nil {
				log.Printf("出错了：%v", err)
			}
			// 设置标题行样式
			if err = f.SetCellStyle(row.Province, "A1", "K1", titleStyle); err != nil {
				log.Printf("出错了：%v", err)
			}
			// 设置列宽
			if err = f.SetColWidth(row.Province, "A", "A", 8); err != nil {
				log.Printf("出错了：%v", err)
			}
			if err = f.SetColWidth(row.Province, "B", "B", 15); err != nil {
				log.Printf("出错了：%v", err)
			}
			if err = f.SetColWidth(row.Province, "C", "C", 35); err != nil {
				log.Printf("出错了：%v", err)
			}
			if err = f.SetColWidth(row.Province, "D", "D", 65); err != nil {
				log.Printf("出错了：%v", err)
			}
			if err = f.SetColWidth(row.Province, "E", "K", 20); err != nil {
				log.Printf("出错了：%v", err)
			}
			//赋值
			if addr, err = excelize.JoinCellName("A", 2); err != nil {
				log.Printf("出错了：%v", err)
			}
			var tempRow []interface{}
			tempRow = append(tempRow, row.Id)
			tempRow = append(tempRow, row.Code)
			tempRow = append(tempRow, row.Name)
			tempRow = append(tempRow, row.Address)
			//tempRow = append(tempRow, amount)
			tempRow = append(tempRow, typeName)
			tempRow = append(tempRow, row.Phone)
			tempRow = append(tempRow, row.Manager)
			tempRow = append(tempRow, row.Agency)
			tempRow = append(tempRow, row.Province)
			tempRow = append(tempRow, row.City)
			tempRow = append(tempRow, row.CreatedAt)
			if err = f.SetSheetRow(row.Province, addr, &tempRow); err != nil {
				log.Printf("出错了：%v", err)
			}
			if err = f.SetSheetRow(sheet, "A"+strconv.Itoa(rk+2), &tempRow); err != nil {
				log.Printf("出错了：%v", err)
			}
			ExistProvince = append(ExistProvince, row.Province)
			ExistProvinceMap[row.Province] = 2
		} else {
			if addr, err = excelize.JoinCellName("A", ExistProvinceMap[row.Province]+1); err != nil {
				log.Printf("出错了：%v", err)
			}
			var tempRow []interface{}
			tempRow = append(tempRow, row.Id)
			tempRow = append(tempRow, row.Code)
			tempRow = append(tempRow, row.Name)
			tempRow = append(tempRow, row.Address)
			//tempRow = append(tempRow, amount)
			tempRow = append(tempRow, typeName)
			tempRow = append(tempRow, row.Phone)
			tempRow = append(tempRow, row.Manager)
			tempRow = append(tempRow, row.Agency)
			tempRow = append(tempRow, row.Province)
			tempRow = append(tempRow, row.City)
			tempRow = append(tempRow, row.CreatedAt)
			if err = f.SetSheetRow(row.Province, addr, &tempRow); err != nil {
				log.Printf("出错了：%v", err)
			}
			if err = f.SetSheetRow(sheet, "A"+strconv.Itoa(rk+2), &tempRow); err != nil {
				log.Printf("出错了：%v", err)
			}
			ExistProvinceMap[row.Province] += 1
		}

		//防止循环过快
		//time.Sleep(50 * time.Millisecond)
	}
	if err = f.SaveAs("终端信息.xlsx"); err != nil {
		log.Printf("出错了：%v", err)
	}
}

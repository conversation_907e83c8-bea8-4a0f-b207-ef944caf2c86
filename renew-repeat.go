package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/xuri/excelize/v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// RepairRecord represents the repair record structure
type RepairRecord struct {
	Barcode           string `gorm:"column:barcode"`
	Number            string `gorm:"column:number"`
	Model             string `gorm:"column:model"`
	ModelID           int    `gorm:"column:model_id"`
	Applicant         int    `gorm:"column:applicant"`
	ApplicantType     string `gorm:"column:applicant_type"`
	TopAgency         int    `gorm:"column:top_agency"`
	SecondAgency      int    `gorm:"column:second_agency"`
	EndpointID        int    `gorm:"column:endpoint_id"`
	Status            string `gorm:"column:status"`
	Issues            string `gorm:"column:issues"`
	DamageDescription string `gorm:"column:damage_description"`
	DamageImages      string `gorm:"column:damage_images"`
}

// DuplicateInfo stores information about duplicate URLs
type DuplicateInfo struct {
	URL     string
	Records []RepairRecord
}

var RepairDB *gorm.DB

func main() {
	// Initialize database connection
	initDB()

	// Get all repair records with damage images
	records := getAllRepairRecords()

	// Find duplicate URLs
	duplicates := findDuplicateURLs(records)

	// Export to Excel
	exportToExcel(duplicates)

	fmt.Println("Export completed successfully!")
}

func initDB() {
	var err error
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	// Using the same DSN pattern from other scripts
	dsn := "yxphp:cJvkmFOmWQ3y3Etk@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare"
	RepairDB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		panic(err)
	}

	sqlDB, _ := RepairDB.DB()
	sqlDB.SetMaxIdleConns(1)
	sqlDB.SetMaxOpenConns(1)
}

// getAllRepairRecords retrieves all repair records that have damage_images
func getAllRepairRecords() []RepairRecord {
	var records []RepairRecord

	// Query the repair table - based on the sample data structure, it's likely named 'repair' or 'warranty_return'
	// You may need to adjust the table name based on your actual database schema
	err := RepairDB.Table("renew_applications").
		Where("damage_images IS NOT NULL AND damage_images != '' AND damage_images != '\\N'").
		Find(&records).Error

	if err != nil {
		log.Printf("Error fetching repair records: %v", err)
		return nil
	}

	fmt.Printf("Found %d records with damage images\n", len(records))
	return records
}

// parseImageURLs extracts URLs from the damage_images JSON field
func parseImageURLs(damageImages string) []string {
	if damageImages == "" || damageImages == "\\N" {
		return nil
	}

	var urls []string
	err := json.Unmarshal([]byte(damageImages), &urls)
	if err != nil {
		log.Printf("Error parsing damage_images JSON: %v, content: %s", err, damageImages)
		return nil
	}

	return urls
}

// findDuplicateURLs identifies URLs that appear in multiple records
func findDuplicateURLs(records []RepairRecord) []DuplicateInfo {
	urlMap := make(map[string][]RepairRecord)

	// Build map of URL to records
	for _, record := range records {
		urls := parseImageURLs(record.DamageImages)
		for _, url := range urls {
			if url != "" {
				urlMap[url] = append(urlMap[url], record)
			}
		}
	}

	// Find duplicates (URLs that appear in more than one record)
	var duplicates []DuplicateInfo
	for url, recordList := range urlMap {
		if len(recordList) > 1 {
			duplicates = append(duplicates, DuplicateInfo{
				URL:     url,
				Records: recordList,
			})
		}
	}

	fmt.Printf("Found %d duplicate URLs across %d total records\n", len(duplicates), len(records))
	return duplicates
}

// exportToExcel exports duplicate URL data to an Excel file
func exportToExcel(duplicates []DuplicateInfo) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			log.Printf("Error closing Excel file: %v", err)
		}
	}()

	sheetName := "Duplicate URLs"
	f.NewSheet(sheetName)
	f.DeleteSheet("Sheet1")

	// Set headers
	headers := []string{
		"重复URL",
		"记录总数",
		"条码",
		"序列号",
		"机型",
		"机型ID",
		"申请人",
		"申请人类型",
		"顶级代理",
		"二级代理",
		"终端ID",
		"状态",
		"问题",
		"损坏描述",
		"所有损坏图片",
	}

	// Write headers
	for i, header := range headers {
		cell := string(rune('A'+i)) + "1"
		f.SetCellValue(sheetName, cell, header)
	}

	// Set column widths
	f.SetColWidth(sheetName, "A", "A", 80) // URL column
	f.SetColWidth(sheetName, "B", "B", 15) // Count column
	f.SetColWidth(sheetName, "C", "O", 20) // Other columns

	row := 2
	for _, duplicate := range duplicates {
		for i, record := range duplicate.Records {
			// For first record of each duplicate URL, show the URL and count
			if i == 0 {
				f.SetCellValue(sheetName, "A"+strconv.Itoa(row), duplicate.URL)
				f.SetCellValue(sheetName, "B"+strconv.Itoa(row), len(duplicate.Records))
			} else {
				// For subsequent records, leave URL and count empty
				f.SetCellValue(sheetName, "A"+strconv.Itoa(row), "")
				f.SetCellValue(sheetName, "B"+strconv.Itoa(row), "")
			}

			// Fill record data
			f.SetCellValue(sheetName, "C"+strconv.Itoa(row), record.Barcode)
			f.SetCellValue(sheetName, "D"+strconv.Itoa(row), record.Number)
			f.SetCellValue(sheetName, "E"+strconv.Itoa(row), record.Model)
			f.SetCellValue(sheetName, "F"+strconv.Itoa(row), record.ModelID)
			f.SetCellValue(sheetName, "G"+strconv.Itoa(row), record.Applicant)
			f.SetCellValue(sheetName, "H"+strconv.Itoa(row), record.ApplicantType)
			f.SetCellValue(sheetName, "I"+strconv.Itoa(row), record.TopAgency)
			f.SetCellValue(sheetName, "J"+strconv.Itoa(row), record.SecondAgency)
			f.SetCellValue(sheetName, "K"+strconv.Itoa(row), record.EndpointID)
			f.SetCellValue(sheetName, "L"+strconv.Itoa(row), record.Status)
			f.SetCellValue(sheetName, "M"+strconv.Itoa(row), record.Issues)
			f.SetCellValue(sheetName, "N"+strconv.Itoa(row), record.DamageDescription)
			f.SetCellValue(sheetName, "O"+strconv.Itoa(row), record.DamageImages)

			row++
		}

		// Add empty row between different duplicate URLs for better readability
		row++
	}

	// Save file
	filename := fmt.Sprintf("duplicate_damage_images_%s.xlsx", time.Now().Format("20060102_150405"))
	if err := f.SaveAs(filename); err != nil {
		log.Printf("Error saving Excel file: %v", err)
		return
	}

	fmt.Printf("Excel file saved as: %s\n", filename)
}

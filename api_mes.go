package main

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"time"
)

const (
	key         = "Web"
	secret      = "M4S8tUB8OBBvIUN7"
	httpTimeout = time.Second * 10
)

func main() {
	number := ""
	barcode := "0010351248546"
	imei := ""

	data, err := checkMESV1(number, barcode, imei)
	if err != nil {
		log.Fatalf("Error: %v", err)
	}

	fmt.Printf("Data: %v\n", data)
}

func checkMESV1(number, barcode, imei string) (map[string]interface{}, error) {
	var params string
	if barcode != "" {
		params = "barcode=" + barcode
	} else if imei != "" {
		params = "imei=" + imei
	} else if number != "" {
		params = "number=" + number
	} else {
		return nil, nil
	}

	url := "http://api-mes.readboy.com/index.php?s=/Api/Barcode/all.html&" + params
	data, err := _request(url)
	if err != nil || data == nil {
		return nil, err
	}

	if errcode, ok := data["errcode"].(string); ok && errcode == "0" {
		if productData, ok := data["data"].(map[string]interface{}); ok {
			productData["product_date"] = productData["update_time"]
			delete(productData, "update_time")
			return productData, nil
		}
	}

	return nil, nil
}

func _request(url string) (map[string]interface{}, error) {
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	hashInput := key + "-" + timestamp + "-" + secret
	hash := md5.Sum([]byte(hashInput))
	sn := hex.EncodeToString(hash[:])
	authKey := key + "-" + timestamp + "-" + sn
	url = url + "&authKey=" + authKey

	client := &http.Client{
		Timeout: httpTimeout,
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var data map[string]interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return nil, err
	}

	return data, nil
}
